document.addEventListener("DOMContentLoaded",function(){console.log("🚀 认证页面已加载");const e="http://103.96.75.196/api";console.log("🌐 API基础URL:",e);const t=document.querySelectorAll(".auth-tab"),o=document.querySelectorAll(".auth-form"),n=document.getElementById("login-form"),a=document.getElementById("register-form"),s=document.getElementById("activation-section"),c=document.getElementById("user-info"),l=document.getElementById("expired-interface"),r=document.getElementById("message"),i=document.getElementById("loading"),d=document.getElementById("purchase-link"),u=document.getElementById("login-username"),g=document.getElementById("login-password"),m=document.getElementById("login-btn"),y=document.getElementById("register-username"),h=document.getElementById("register-password"),f=document.getElementById("register-confirm"),p=document.getElementById("register-btn"),v=document.getElementById("user-name"),w=document.getElementById("user-status"),_=document.getElementById("goto-main-btn"),E=document.getElementById("test-jump-btn"),L=document.getElementById("logout-btn"),I=document.getElementById("activation-code"),k=document.getElementById("activate-btn"),b=document.getElementById("expired-activation-code"),B=document.getElementById("expired-activate-btn");let S=null;function P(e){t.forEach(t=>{t.classList.remove("active"),t.dataset.tab===e&&t.classList.add("active")}),o.forEach(t=>{t.classList.remove("active"),t.id===`${e}-form`&&t.classList.add("active")})}async function T(e,t={}){const o={method:"GET",headers:{"Content-Type":"application/json"},...t},n=await fetch(e,o);return await n.json()}async function $(){console.log("🔑 开始处理登录...");const t=u.value.trim(),o=g.value.trim();if(console.log("用户名:",t),console.log("密码长度:",o.length),t&&o){J(!0);try{console.log("🌐 发送登录请求到:",`${e}/login.php`);const n=await T(`${e}/login.php`,{method:"POST",body:JSON.stringify({username:t,password:o})});console.log("📥 登录响应:",n),n.success?(S=n.data,await R(S),S.remaining_days>0&&"active"===S.status?await z(!0,S):await z(!1),j("登录成功！","success"),console.log("✅ 登录成功，准备跳转..."),setTimeout(()=>{S.remaining_days>0?(console.log("🚀 登录成功，跳转到主界面..."),H()):(console.log("⏰ 用户已过期，显示过期界面"),C(S))},1e3)):(console.log("❌ 登录失败:",n.message),j(n.message,"error"),n.data&&n.data.expired&&n.data.user_data&&(M(n.data.purchase_link),await C(n.data.user_data)))}catch(e){console.error("登录请求失败:",e),j("登录失败，请检查网络连接","error")}finally{J(!1)}}else j("请填写用户名和密码","error")}async function x(){console.log("📝 开始处理注册...");const t=y.value.trim(),o=h.value.trim(),n=f.value.trim();if(console.log("注册用户名:",t),console.log("密码长度:",o.length),console.log("确认密码长度:",n.length),t&&o&&n)if(o===n)if(t.length<3||t.length>20)j("用户名长度必须在3-20个字符之间","error");else if(/^[a-zA-Z0-9_]+$/.test(t))if(o.length<6)j("密码长度至少6个字符","error");else{J(!0);try{console.log("🌐 发送注册请求到:",`${e}/register.php`);const n=await T(`${e}/register.php`,{method:"POST",body:JSON.stringify({username:t,password:o})});console.log("📥 注册响应:",n),n.success?"expired"===n.data.status||n.data.remaining_days<=0?(j("注册成功！","success"),S=n.data,await R(S),setTimeout(()=>{C(n.data)},1500)):(j("注册成功！请登录","success"),y.value="",h.value="",f.value="",setTimeout(()=>{P("login"),u.value=t},1500)):j(n.message,"error")}catch(e){console.error("注册请求失败:",e),j("注册失败，请检查网络连接","error")}finally{J(!1)}}else j("用户名只能包含字母、数字和下划线","error");else j("两次输入的密码不一致","error");else j("请填写所有字段","error")}async function O(){const t=I.value.trim();if(t)if(S&&S.user_id){J(!0);try{const o=await T(`${e}/activate.php`,{method:"POST",body:JSON.stringify({user_id:S.user_id,activation_code:t})});o.success?(S=o.data,await R(S),S.remaining_days>0&&"active"===S.status?await z(!0,S):await z(!1),j(`激活成功！增加了${o.data.days_added}天使用时间`,"success"),I.value="",U(),S.remaining_days>0&&setTimeout(()=>{console.log("🚀 激活成功，跳转到主界面..."),H()},2e3)):j(o.message,"error")}catch(e){console.error("激活失败:",e),j("激活失败，请检查网络连接","error")}finally{J(!1)}}else j("请先登录","error");else j("请输入激活码","error")}async function A(){const t=b.value.trim();if(!t)return void j("请输入激活码","error");const o=await q();if(o&&o.user_id){B.disabled=!0,B.textContent="激活中...";try{const n=await T(`${e}/activate.php`,{method:"POST",body:JSON.stringify({user_id:o.user_id,activation_code:t})});n.success?(S=n.data,await R(S),S.remaining_days>0&&"active"===S.status?await z(!0,S):await z(!1),j(`激活成功！增加了${n.data.days_added}天使用时间`,"success"),setTimeout(()=>{console.log("🚀 过期用户激活成功，跳转到主界面..."),H()},1500)):j(n.message,"error")}catch(e){console.error("激活失败:",e),j("激活失败，请检查网络连接","error")}finally{B.disabled=!1,B.textContent="✨ 激活"}}else j("用户信息丢失，请重新登录","error")}async function D(){await G(),await z(!1),j("已退出登录","success"),setTimeout(()=>{location.reload()},1e3)}async function C(e){N(),l.classList.add("show"),e&&(S=e,await R(e),window.currentPurchaseLink&&(e.purchase_link=window.currentPurchaseLink),console.log("🚫 用户账户已过期，自动停用插件功能"),await async function(){try{console.log("🚫 用户账户已过期，自动停用插件功能");try{await chrome.runtime.sendMessage({type:"DISABLE_PLUGIN_FOR_EXPIRED_USER"})}catch(e){console.log("无法通知background script，直接在当前页面停用")}localStorage.setItem("plugin_manually_activated","false"),localStorage.removeItem("plugin_activation_time"),localStorage.removeItem("plugin_auth_status"),localStorage.removeItem("plugin_user_info"),console.log("✅ 插件功能已停用")}catch(e){console.error("❌ 停用插件功能失败:",e)}}())}function N(){n.classList.remove("active"),a.classList.remove("active"),c.classList.remove("show"),s.classList.remove("show"),l.classList.remove("show"),document.querySelector(".auth-tabs").style.display="none"}function U(){S&&(v.textContent=S.username,w.textContent=`剩余天数: ${S.remaining_days} 天 | 状态: ${S.status}`)}function M(e){e&&d&&(d.href=e)}function j(e,type){r.textContent=e,r.className=`message ${type}`,r.style.display="block",setTimeout(()=>{r.style.display="none"},3e3)}function J(e){e?(i.classList.add("show"),m.disabled=!0,p.disabled=!0,k.disabled=!0):(i.classList.remove("show"),m.disabled=!1,p.disabled=!1,k.disabled=!1)}async function R(e){try{await chrome.storage.local.set({user_data:e,login_time:Date.now()})}catch(e){console.error("存储用户数据失败:",e)}}async function q(){try{return(await chrome.storage.local.get(["user_data"])).user_data}catch(e){return console.error("获取用户数据失败:",e),null}}async function G(){try{await chrome.storage.local.remove(["user_data","login_time"])}catch(e){console.error("清除用户数据失败:",e)}}async function z(e,t=null){try{await chrome.runtime.sendMessage({type:"AUTH_STATUS_CHANGE",authorized:e,userData:t})}catch(e){console.error("通知授权状态变化失败:",e)}}function M(e){window.currentPurchaseLink=e,console.log("购买链接已更新:",e),d&&e&&"#"!==e&&(d.href=e)}async function H(){console.log("🚀 认证成功，跳转到主界面..."),window.location.href="popup.html"}console.log("🔍 验证DOM元素:"),console.log("- 登录表单:",n?"✅":"❌"),console.log("- 注册表单:",a?"✅":"❌"),console.log("- 登录按钮:",m?"✅":"❌"),console.log("- 注册按钮:",p?"✅":"❌"),console.log("- 登录用户名输入:",u?"✅":"❌"),console.log("- 登录密码输入:",g?"✅":"❌"),console.log("🔧 开始绑定事件监听器..."),console.log("DOM元素检查:"),console.log("- authTabs数量:",t.length),console.log("- loginBtn:",m),console.log("- registerBtn:",p),console.log("- loginUsername:",u),console.log("- loginPassword:",g),t.length>0&&(t.forEach((e,t)=>{console.log(`绑定标签页 ${t}:`,e.dataset.tab),e.addEventListener("click",()=>{console.log("标签页被点击:",e.dataset.tab),P(e.dataset.tab)})}),console.log("✅ 标签页事件已绑定")),m?(m.addEventListener("click",e=>{console.log("🔑 登录按钮被点击"),e.preventDefault(),$()}),console.log("✅ 登录按钮事件已绑定")):console.error("❌ 登录按钮未找到"),p?(p.addEventListener("click",e=>{console.log("📝 注册按钮被点击"),e.preventDefault(),x()}),console.log("✅ 注册按钮事件已绑定")):console.error("❌ 注册按钮未找到"),k.addEventListener("click",O),B.addEventListener("click",A),_&&_.addEventListener("click",()=>{console.log("🚀 主界面按钮被点击"),H()}),E&&E.addEventListener("click",()=>{console.log("🧪 测试跳转按钮被点击"),H()}),L&&L.addEventListener("click",D),d&&d.addEventListener("click",t=>{t.preventDefault(),console.log("🛒 购买链接被点击"),console.log("当前用户:",S),console.log("全局购买链接:",window.currentPurchaseLink);let o=null;if(S&&S.purchase_link&&"#"!==S.purchase_link)o=S.purchase_link,console.log("使用用户购买链接:",o);else{if(!window.currentPurchaseLink||"#"===window.currentPurchaseLink)return void async function(){console.log("🛒 尝试从API获取购买链接");try{const t=await fetch(`${e}/get_config.php?key=purchase_link`),o=await t.json();let n="https://xoxome.online/?page_id=1685";o.success&&o.data&&o.data.config_value?(n=o.data.config_value,console.log("从API获取到购买链接:",n)):console.log("API未返回购买链接，使用默认链接"),chrome.tabs.create({url:n})}catch(e){console.error("获取购买链接失败:",e),chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"})}}();o=window.currentPurchaseLink,console.log("使用全局购买链接:",o)}o?chrome.tabs.create({url:o}):(console.log("没有购买链接，使用默认链接"),chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"}))}),g.addEventListener("keypress",e=>{"Enter"===e.key&&$()}),f.addEventListener("keypress",e=>{"Enter"===e.key&&x()}),I.addEventListener("keypress",e=>{"Enter"===e.key&&O()}),b.addEventListener("keypress",e=>{"Enter"===e.key&&A()}),async function(){const t=await q();if(t&&t.user_id)try{const o=await T(`${e}/user_status.php?user_id=${t.user_id}`);o.success?(S=o.data,o.data.remaining_days>0&&"active"===o.data.status?(console.log("✅ 用户已激活，跳转到主界面"),window.location.href="popup.html"):C(o.data)):G()}catch(e){console.error("检查登录状态失败:",e),G()}}()});