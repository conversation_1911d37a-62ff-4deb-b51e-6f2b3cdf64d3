async function checkUserStatusOnPopupOpen(){try{const e=window.PLUGIN_USER_INFO||JSON.parse(localStorage.getItem("plugin_user_info")||"{}");return!(e&&void 0!==e.remaining_days&&e.remaining_days<=0)||(console.log("🚫 检测到用户已过期，自动停用插件"),await disablePluginForExpiredUser(),showExpiredUserMessage(),!1)}catch(e){return console.error("检查用户状态失败:",e),!0}}async function disablePluginForExpiredUser(){try{const e=await chrome.runtime.sendMessage({type:"HANDLE_EXPIRED_USER"});e&&e.success&&console.log("✅ 过期用户处理完成")}catch(e){console.error("处理过期用户失败:",e)}}function showExpiredUserMessage(){const e=document.querySelectorAll(".region-card"),t=document.getElementById("apply-btn"),n=document.getElementById("disable-btn"),o=document.getElementById("clean-augment-btn"),a=document.getElementById("tutorial-btn"),s=document.getElementById("mail-btn"),c=document.getElementById("buy-days-btn"),i=document.getElementById("region-toggle"),l=document.getElementById("logout-btn");e.forEach(e=>{e.style.opacity="0.5",e.style.pointerEvents="none"}),t&&(t.disabled=!0,t.style.opacity="0.5",t.style.cursor="not-allowed"),n&&(n.disabled=!0,n.style.opacity="0.5",n.style.cursor="not-allowed"),o&&(o.disabled=!0,o.style.opacity="0.5",o.style.cursor="not-allowed",o.title="账户已过期，功能不可用"),a&&(a.disabled=!0,a.style.opacity="0.5",a.style.cursor="not-allowed"),s&&(s.disabled=!0,s.style.opacity="0.5",s.style.cursor="not-allowed"),c&&(c.disabled=!0,c.style.opacity="0.5",c.style.cursor="not-allowed"),i&&(i.disabled=!0,i.style.opacity="0.5",i.style.cursor="not-allowed"),l&&(l.disabled=!0,l.style.opacity="0.5",l.style.cursor="not-allowed");const r=document.createElement("div");r.id="expired-notice",r.style.cssText="\n        background: #f8d7da;\n        color: #721c24;\n        border: 1px solid #f5c6cb;\n        border-radius: 4px;\n        padding: 15px;\n        margin-bottom: 15px;\n        text-align: center;\n        font-weight: bold;\n    ",r.innerHTML='\n        <div style="margin-bottom: 10px;">⚠️ 账户已过期</div>\n        <div style="font-size: 12px; font-weight: normal;">\n            您的账户使用期限已到期，所有功能已停用。<br>\n            请联系客服续费或重新购买。\n        </div>\n    ';const d=document.querySelector(".container")||document.body;d.insertBefore(r,d.firstChild)}document.addEventListener("DOMContentLoaded",async function(){if(!await checkUserStatusOnPopupOpen())return;const e=document.querySelectorAll(".region-card"),t=document.getElementById("apply-btn"),n=document.getElementById("disable-btn"),o=document.getElementById("clean-augment-btn"),a=document.getElementById("clean-status"),s=document.getElementById("tutorial-btn"),c=document.getElementById("mail-btn"),i=document.getElementById("buy-days-btn"),l=document.getElementById("region-toggle"),r=document.getElementById("region-content"),d=document.getElementById("author-text"),u=document.getElementById("current-location"),m=document.getElementById("current-time"),g=document.getElementById("current-details"),y=document.getElementById("status"),p=document.getElementById("loading"),h=document.getElementById("user-info"),f=document.getElementById("user-welcome"),b=document.getElementById("logout-btn"),w=document.getElementById("buy-days-modal"),E=document.getElementById("activation-input"),_=document.getElementById("activate-btn"),v=document.getElementById("cancel-btn"),I=document.getElementById("purchase-btn");let S=null;const x={US:{name:"美国",flag:"🇺🇸",timezones:["America/New_York","America/Chicago","America/Denver","America/Los_Angeles"],locale:"en-US",country:"United States"},TW:{name:"台湾",flag:"🇹🇼",timezones:["Asia/Taipei"],locale:"zh-TW",country:"Taiwan"},JP:{name:"日本",flag:"🇯🇵",timezones:["Asia/Tokyo"],locale:"ja-JP",country:"Japan"},SG:{name:"新加坡",flag:"🇸🇬",timezones:["Asia/Singapore"],locale:"en-SG",country:"Singapore"}};async function C(){try{const e=(await chrome.storage.local.get(["user_data"])).user_data;if(e&&e.username&&e.user_id)try{const t="http://*************/api",n=await fetch(`${t}/user_status.php?user_id=${e.user_id}`),o=await n.json();if(o.success){const e=o.data,t=e.remaining_days>0?`剩余${e.remaining_days}天`:"已过期";f.textContent=`欢迎，${e.username} (${t})`,h.style.display="flex",e.remaining_days<=0||"expired"===e.status?(console.log("🚫 检测到用户账户已过期，自动停用插件功能"),await async function(){try{console.log("🚫 用户账户已过期，自动停用插件功能");const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{sessionStorage.setItem("plugin_manually_activated","false"),sessionStorage.removeItem("plugin_activation_time"),console.log("🚫 插件功能已因账户过期而自动停用")}})}catch(e){}A(!1);try{await chrome.runtime.sendMessage({type:"PLUGIN_DEACTIVATED"})}catch(e){console.warn("无法发送停用状态消息:",e)}console.log("✅ 插件功能已为所有标签页停用")}catch(e){console.error("❌ 停用插件功能失败:",e)}}(),F()):j(),e.remaining_days<7?(b.style.display="none",console.log("🔒 剩余天数少于7天，隐藏退出按钮")):b.style.display="block",await chrome.storage.local.set({user_data:e,login_time:Date.now()}),console.log("✅ 用户信息已更新:",e)}else{console.log("⚠️ 服务器验证失败，使用本地数据");const t=e.remaining_days>0?`剩余${e.remaining_days}天`:"已过期";f.textContent=`欢迎，${e.username} (${t})`,h.style.display="flex",e.remaining_days<7?b.style.display="none":b.style.display="block",e.remaining_days<=0||"expired"===e.status?F():j()}}catch(t){console.error("获取最新用户状态失败:",t);const n=e.remaining_days>0?`剩余${e.remaining_days}天`:"已过期";f.textContent=`欢迎，${e.username} (${n})`,h.style.display="flex",e.remaining_days<7?b.style.display="none":b.style.display="block",e.remaining_days<=0||"expired"===e.status?F():j()}}catch(e){console.error("加载用户信息失败:",e)}}function T(){e.forEach(e=>{e.classList.remove("selected"),e.dataset.region===S&&e.classList.add("selected")})}function L(e){if(e.selectedCity&&e.selectedRegion){const t=x[e.selectedRegion];u.textContent=`${t.flag} ${e.selectedCity.city}, ${e.selectedCity.country}`,g.textContent=`时区: ${e.selectedCity.timezone} | 语言: ${e.selectedCity.locale}`,y.classList.add("active")}}async function k(){if(t.disabled)alert("您的账户已过期，无法使用此功能。请点击上面小钻石图标充值。");else if(S){U(!0);try{await chrome.runtime.sendMessage({type:"PLUGIN_WORKING"})}catch(e){console.warn("无法发送工作状态消息:",e)}try{const e=x[S],n=e.timezones[Math.floor(Math.random()*e.timezones.length)],o=function(e,t,n){const o={US:["New York","Los Angeles","Chicago","Houston","Phoenix"],TW:["Taipei","Kaohsiung","Taichung","Tainan","Taoyuan"],JP:["Tokyo","Osaka","Yokohama","Nagoya","Sapporo"],SG:["Singapore","Jurong West","Woodlands","Tampines","Sengkang"]}[e],a={US:{lat:40.7128,lng:-74.006},TW:{lat:25.033,lng:121.5654},JP:{lat:35.6762,lng:139.6503},SG:{lat:1.3521,lng:103.8198}}[e];return{city:o[Math.floor(Math.random()*o.length)],state:"US"===e?"NY":n.name,country:n.country,country_code:e,timezone:t,locale:n.locale,lat:a.lat+.2*(Math.random()-.5),lng:a.lng+.2*(Math.random()-.5)}}(S,n,e);await chrome.storage.sync.set({selectedRegion:S,selectedCity:o,lastUpdate:Date.now()});try{console.log("🌍 通过background script应用设置到所有标签页..."),await chrome.runtime.sendMessage({type:"APPLY_PLUGIN_SETTINGS",cityData:o}),console.log("✅ 设置已通过background script应用到所有标签页")}catch(e){console.warn("通过background script应用设置失败，使用备用方法:",e);const t="globalTimezoneSpoofer_selectedCity",tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:(key,e)=>{localStorage.setItem(key,JSON.stringify(e)),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),console.log("🔄 Updated localStorage and cleared fingerprint for new settings"),console.log("✅ Plugin will auto-activate with new region settings")},args:[t,o]})}catch(e){}}if(await new Promise(e=>setTimeout(e,500)),L({selectedRegion:S,selectedCity:o}),confirm("设置已应用到所有标签页！是否刷新现有标签页以立即生效？\n（新打开的标签页会自动应用设置）")){const tabs=await chrome.tabs.query({});for(const e of tabs)try{e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||await chrome.tabs.reload(e.id)}catch(e){}}U(!1),A(!0),setTimeout(async()=>{try{await chrome.runtime.sendMessage({type:"PLUGIN_ACTIVATED"})}catch(e){console.warn("无法发送激活状态消息:",e)}},1e3),function(){const e=t.textContent;t.textContent="✅ 设置已应用",t.style.background="#4CAF50",setTimeout(()=>{t.textContent=e,t.style.background=""},2e3)}()}catch(e){console.error("Failed to apply settings:",e),alert("应用设置失败，请重试"),U(!1)}}else alert("请先选择一个地区")}async function B(){if(confirm('确定要停用插件功能吗？停用后需要重新点击"应用设置"来启用。')){U(!0);try{await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log("✅ 已清除sync storage中的地区设置");try{await chrome.storage.session.set({plugin_globally_activated:!1}),console.log("✅ 全局激活状态已清除")}catch(e){console.log("⚠️ chrome.storage.session不可用")}const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),console.log("⏸️ Plugin disabled by removing region settings")}})}catch(e){}A(!1);try{await chrome.runtime.sendMessage({type:"PLUGIN_DEACTIVATED"})}catch(e){console.warn("无法发送停用状态消息:",e)}U(!1),alert("插件功能已停用")}catch(e){console.error("Failed to disable plugin:",e),alert("停用插件失败，请重试"),U(!1)}}}function A(e){e?(t.style.display="inline-block",n.style.display="inline-block",t.textContent="重新应用设置 / Reapply Settings"):(t.style.display="inline-block",n.style.display="none",t.textContent="应用设置 / Apply Settings")}function U(e){e?(p.style.display="block",t.disabled=!0):(p.style.display="none",t.disabled=!1)}function P(){const now=new Date;m.textContent=now.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})}async function D(){const e=window.PLUGIN_USER_INFO||JSON.parse(localStorage.getItem("plugin_user_info")||"{}");if(e&&void 0!==e.remaining_days&&e.remaining_days<=0)return N("❌ 账户已过期，功能不可用","error"),void setTimeout(()=>{$()},3e3);try{N("正在清理Augment相关数据...","progress"),o.disabled=!0;const e={origins:["https://*.augmentcode.com","http://*.augmentcode.com","https://*.augment.com","http://*.augment.com","https://augmentcode.com","http://augmentcode.com","https://augment.com","http://augment.com"]},t=[];t.push(chrome.browsingData.removeCookies(e).catch(e=>console.warn("清理Cookies失败:",e))),t.push(chrome.browsingData.removeLocalStorage(e).catch(e=>console.warn("清理LocalStorage失败:",e))),t.push(chrome.browsingData.removeCache(e).catch(e=>console.warn("清理Cache失败:",e))),t.push(chrome.browsingData.removeIndexedDB(e).catch(e=>console.warn("清理IndexedDB失败:",e))),t.push(chrome.browsingData.removeWebSQL(e).catch(e=>console.warn("清理WebSQL失败:",e))),await Promise.all(t);try{const tabs=await chrome.tabs.query({});for(const e of tabs)if(e.url&&(e.url.includes("augmentcode.com")||e.url.includes("augment.com")))try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{try{localStorage.clear(),sessionStorage.clear(),console.log("🧹 页面存储数据已清理")}catch(e){console.warn("清理页面存储失败:",e)}}})}catch(e){}}catch(e){console.warn("清理标签页数据失败:",e)}N("✅ Augment数据清理完成！","success"),setTimeout(()=>{$()},3e3)}catch(e){console.error("清理Augment数据失败:",e),N("❌ 清理失败，请重试","error"),setTimeout(()=>{$()},3e3)}finally{o.disabled=!1}}function N(e,type){a.style.display="block",a.className=`clean-status ${type}`,a.innerHTML="progress"===type?`<div class="clean-progress">${e}</div>`:`<div>${e}</div>`}function $(){a.style.display="none"}function M(){chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"})}function z(){chrome.tabs.create({url:"https://mail.xoxome.online"})}function G(){w.style.display="flex",E.addEventListener("input",()=>{const e=E.value.trim();_.disabled=!e}),_.addEventListener("click",R),I.addEventListener("click",W),v.addEventListener("click",O),w.addEventListener("click",e=>{e.target===w&&O()}),E.addEventListener("keypress",e=>{"Enter"!==e.key||_.disabled||R()})}function O(){w.style.display="none",E.value="",_.disabled=!0}async function R(){const e=E.value.trim();if(e){_.disabled=!0,_.textContent="激活中...";try{const t=await async function(){try{return(await chrome.storage.local.get(["user_data"])).user_data}catch(e){return console.error("获取用户数据失败:",e),null}}();if(!t||!t.user_id)return void alert("请先登录");const n=await async function(e,t={}){const n={method:"GET",headers:{"Content-Type":"application/json"}},o=await fetch(e,{...n,...t});if(!o.ok&&402!==o.status)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const a=await o.json();return console.log("API响应:",{status:o.status,data:a}),a}("http://*************/api/activate.php",{method:"POST",body:JSON.stringify({user_id:t.user_id,activation_code:e})});n.success?(alert(`激活成功！增加了${n.data.days_added}天使用时间`),await chrome.storage.local.set({user_data:n.data,login_time:Date.now()}),await chrome.runtime.sendMessage({type:"AUTH_STATUS_CHANGE",authorized:!0,userData:n.data}),O(),await C()):alert(n.message||"激活失败")}catch(e){console.error("激活失败:",e),alert("激活失败，请检查网络连接")}finally{_.disabled=!1,_.textContent="立即激活"}}}async function W(){try{const e=await fetch("http://*************/api/get_config.php?key=purchase_link"),t=await e.json();let n="https://xoxome.online/?page_id=1685";t.success&&t.data&&t.data.config_value&&(n=t.data.config_value),chrome.tabs.create({url:n}),O()}catch(e){console.error("获取购买链接失败:",e),chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"}),O()}}function J(){l.classList.contains("collapsed")?(l.classList.remove("collapsed"),r.classList.remove("collapsed"),r.classList.add("expanded")):(l.classList.add("collapsed"),r.classList.remove("expanded"),r.classList.add("collapsed"))}function H(){chrome.tabs.create({url:"https://browserleaks.com/webrtc"})}async function q(){if(confirm("确定要退出登录吗？"))try{await chrome.storage.local.remove(["user_data","login_time"]),await chrome.runtime.sendMessage({type:"AUTH_STATUS_CHANGE",authorized:!1}),window.location.href="auth.html"}catch(e){console.error("退出登录失败:",e),alert("退出登录失败，请重试")}}function F(){t&&(t.disabled=!0,t.style.opacity="0.5",t.style.cursor="not-allowed",t.textContent="账户已过期 / Account Expired",console.log("🚫 应用设置按钮已禁用（账户过期）"))}function j(){t&&(t.disabled=!1,t.style.opacity="1",t.style.cursor="pointer",t.textContent="应用设置 / Apply Settings",console.log("✅ 应用设置按钮已启用"))}!async function(){console.log("🔍 检查DOM元素:"),console.log("- cleanAugmentBtn:",o?"✅":"❌"),console.log("- applyBtn:",t?"✅":"❌"),console.log("- tutorialBtn:",s?"✅":"❌"),console.log("- mailBtn:",c?"✅":"❌"),console.log("- buyDaysBtn:",i?"✅":"❌"),await async function(){try{const e=await chrome.storage.sync.get(["selectedRegion","selectedCity","lastUpdate"]);e.selectedRegion?(S=e.selectedRegion,T(),L(e)):(S="US",T(),u.textContent="未设置",g.textContent="请选择一个地区并应用设置")}catch(e){console.error("Failed to load settings:",e),u.textContent="加载失败",g.textContent="无法加载当前设置"}}(),await C(),e.forEach(e=>{e.addEventListener("click",()=>{S=e.dataset.region,T()})}),t.addEventListener("click",k),n.addEventListener("click",B),o.addEventListener("click",D),s.addEventListener("click",M),c.addEventListener("click",z),i.addEventListener("click",G),l.addEventListener("click",J),d.addEventListener("dblclick",H),b.addEventListener("click",q),await async function(){try{(await chrome.storage.sync.get(["selectedCity"])).selectedCity?A(!0):A(!1)}catch(e){A(!1)}}(),P(),setInterval(P,1e3)}()});