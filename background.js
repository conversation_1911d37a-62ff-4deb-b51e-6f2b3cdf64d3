let currentIconState="inactive",flashInterval=null;async function setIconState(e){if(currentIconState!==e){currentIconState=e,flashInterval&&(clearInterval(flashInterval),flashInterval=null);try{switch(e){case"inactive":await chrome.action.setBadgeText({text:""}),await chrome.action.setBadgeBackgroundColor({color:"#888888"}),await chrome.action.setTitle({title:"Global Timezone & Location Spoofer - 未启用"});break;case"active":await chrome.action.setBadgeText({text:"●"}),await chrome.action.setBadgeBackgroundColor({color:"#28a745"}),await chrome.action.setTitle({title:"Global Timezone & Location Spoofer - 已启用"});break;case"working":await chrome.action.setTitle({title:"Global Timezone & Location Spoofer - 工作中"});let e=!1;flashInterval=setInterval(async()=>{try{e?(await chrome.action.setBadgeText({text:"◉"}),await chrome.action.setBadgeBackgroundColor({color:"#00ff00"})):(await chrome.action.setBadgeText({text:"●"}),await chrome.action.setBadgeBackgroundColor({color:"#28a745"})),e=!e}catch(e){console.warn("徽章闪烁更新失败:",e)}},500)}console.log(`🎨 徽章状态已更新为: ${e}`)}catch(e){console.warn("设置徽章失败:",e)}}}chrome.runtime.onInstalled.addListener(()=>{console.log("🌍 Global Timezone & Location Spoofer v5.2.2 installed"),console.log("🛡️ 多地区支持版：美国、台湾、日本、新加坡时区和位置伪装"),console.log("👨‍💻 作者：小鱼游水 | 🌐 网址：https://xoxome.online"),setIconState("inactive"),chrome.storage.sync.get(["selectedRegion"],e=>{e.selectedRegion||(console.log("🎯 Setting default region to US"),chrome.storage.sync.set({selectedRegion:"US",lastUpdate:Date.now()}))}),chrome.action.setPopup({popup:"auth.html"})}),chrome.runtime.onStartup.addListener(()=>{console.log("🚀 浏览器启动，检查用户认证状态"),setIconState("inactive"),checkAuthAndSetUI()}),chrome.tabs.onCreated.addListener(async e=>{console.log("📄 新标签页创建:",e.id),await injectPluginStatusToTab(e.id)}),chrome.tabs.onUpdated.addListener(async(e,t,o)=>{"loading"!==t.status||!o.url||o.url.startsWith("chrome://")||o.url.startsWith("chrome-extension://")||(console.log("🔄 标签页开始加载，立即注入设置:",e,o.url),await injectPluginStatusToTab(e),setTimeout(async()=>{await injectPluginStatusToTab(e)},50)),"complete"!==t.status||!o.url||o.url.startsWith("chrome://")||o.url.startsWith("chrome-extension://")||(console.log("🔄 标签页加载完成，确认设置注入:",e,o.url),await injectPluginStatusToTab(e))}),chrome.runtime.onMessage.addListener((e,t,o)=>{if("AUTH_STATUS_CHANGE"===e.type)e.authorized&&e.userData?(generateAuthFile(e.userData),chrome.action.setPopup({popup:"popup.html"})):(removeAuthFile(),chrome.action.setPopup({popup:"auth.html"}),setIconState("inactive")),o({success:!0});else{if("CHECK_AUTH_STATUS"===e.type)return checkAuthAndSetUI().then(()=>{o({success:!0})}),!0;if("DISABLE_PLUGIN_FOR_EXPIRED_USER"===e.type)return disablePluginForExpiredUser().then(()=>{o({success:!0})}),!0;if("APPLY_PLUGIN_SETTINGS"===e.type)return applyPluginSettingsToAllTabs(e.cityData).then(()=>{o({success:!0})}),!0;if("PLUGIN_ACTIVATED"===e.type)setIconState("active"),o({success:!0});else if("PLUGIN_DEACTIVATED"===e.type)setIconState("inactive"),o({success:!0});else if("PLUGIN_WORKING"===e.type)setIconState("working"),o({success:!0});else{if("HANDLE_EXPIRED_USER"===e.type)return handleExpiredUserFromPopup().then(e=>{o({success:e})}),!0;if("CLEAR_EXPIRED_USER_SETTINGS"===e.type)return clearExpiredUserSettings().then(e=>{o({success:e})}),!0}}});let hasCheckedAuthThisSession=!1;async function checkAuthAndSetUI(){if(hasCheckedAuthThisSession)console.log("⏭️ 本次会话已检查过认证状态，跳过");else try{console.log("🔍 开始检查用户认证状态（仅浏览器启动时）"),hasCheckedAuthThisSession=!0;const e=await getStoredUserData();if(!e||!e.user_id)return console.log("🔒 用户未登录，设置为认证页面"),void chrome.action.setPopup({popup:"auth.html"});const t="http://103.96.75.196/api",o=await fetch(`${t}/user_status.php?user_id=${e.user_id}`),a=await o.json();a.success&&a.data.remaining_days>0&&"active"===a.data.status?(console.log("✅ 用户认证通过，设置为主界面"),await generateAuthFile(a.data),chrome.action.setPopup({popup:"popup.html"})):(console.log("🔒 用户认证失败或已过期，设置为认证页面"),await disablePluginForExpiredUser(),chrome.action.setPopup({popup:"auth.html"}))}catch(e){console.error("❌ 认证检查失败:",e);const t=await getStoredUserData();t&&t.user_id?(console.log("🔄 网络错误，使用本地用户数据设置为主界面"),chrome.action.setPopup({popup:"popup.html"})):chrome.action.setPopup({popup:"auth.html"})}}async function generateAuthFile(e){try{console.log("📝 注入用户信息到标签页...");const tabs=await chrome.tabs.query({});for(const t of tabs)try{await chrome.scripting.executeScript({target:{tabId:t.id},func:e=>{window.PLUGIN_USER_INFO=e,localStorage.setItem("plugin_user_info",JSON.stringify(e)),console.log("📝 用户信息已注入:",e.username,"剩余天数:",e.remaining_days)},args:[{username:e.username,remaining_days:e.remaining_days,status:e.status,authorized_at:(new Date).toISOString()}]})}catch(e){}await chrome.storage.local.set({auth_user_info:e,auth_updated_at:Date.now()}),console.log("✅ 用户信息已注入到所有标签页")}catch(e){console.error("❌ 注入用户信息失败:",e)}}async function removeAuthFile(){try{console.log("🧹 清除用户信息...");const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{delete window.PLUGIN_USER_INFO,localStorage.removeItem("plugin_user_info"),console.log("🧹 用户信息已清除")}})}catch(e){}await chrome.storage.local.remove(["auth_user_info","auth_updated_at"]),console.log("✅ 用户信息已从所有标签页清除")}catch(e){console.error("❌ 清除用户信息失败:",e)}}async function getStoredUserData(){try{return(await chrome.storage.local.get(["user_data"])).user_data}catch(e){return console.error("获取用户数据失败:",e),null}}async function disablePluginForExpiredUser(){try{console.log("🚫 用户账户已过期，自动停用插件功能"),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log("✅ 已清除sync storage中的地区设置");try{await chrome.storage.session.set({plugin_globally_activated:!1}),console.log("✅ 全局激活状态已清除")}catch(e){console.log("⚠️ chrome.storage.session不可用")}const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("plugin_auth_status"),localStorage.removeItem("plugin_user_info"),localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),localStorage.setItem("plugin_expired_handled","true"),console.log("🚫 插件功能已因账户过期而自动停用")}})}catch(e){}console.log("✅ 插件功能已为所有标签页停用")}catch(e){console.error("❌ 停用插件功能失败:",e)}}async function handleExpiredUserFromPopup(){try{console.log("🚫 从popup触发：处理过期用户"),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]);const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),localStorage.removeItem("plugin_user_info"),localStorage.removeItem("plugin_auth_status"),localStorage.setItem("plugin_expired_handled","true")}})}catch(e){}return!0}catch(e){return console.error("处理过期用户失败:",e),!1}}async function clearExpiredUserSettings(){try{return console.log("🚫 从content script触发：清除过期用户设置"),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log("✅ 已清除sync storage中的过期用户设置"),!0}catch(e){return console.error("清除过期用户设置失败:",e),!1}}async function applyPluginSettingsToAllTabs(e){try{console.log("🌍 应用插件设置到所有标签页..."),await chrome.storage.sync.set({selectedCity:e}),console.log("✅ 地区设置已保存到sync storage");const tabs=await chrome.tabs.query({});for(const t of tabs)try{await chrome.scripting.executeScript({target:{tabId:t.id},func:(key,e)=>{localStorage.setItem(key,JSON.stringify(e)),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),console.log("🔄 Updated localStorage with new settings - plugin will auto-activate"),"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage({type:"REGION_SETTINGS_UPDATED",cityData:e}).catch(()=>{})},args:["globalTimezoneSpoofer_selectedCity",e]})}catch(e){console.log("⚠️ 无法访问标签页:",t.id,e.message)}console.log("✅ 插件设置已应用到所有标签页")}catch(e){console.error("❌ 应用插件设置失败:",e)}}async function injectPluginStatusToTab(e,t=0){try{const o=await chrome.storage.sync.get(["selectedCity"]),a=await chrome.storage.local.get(["auth_user_info"]);if(!o.selectedCity)return void console.log("⚠️ 没有地区设置，跳过注入:",e);const c=await chrome.scripting.executeScript({target:{tabId:e},func:(e,t)=>{try{return e&&(localStorage.setItem("globalTimezoneSpoofer_selectedCity",JSON.stringify(e)),console.log("📝 新标签页地区设置已注入，插件将自动启用")),t&&(window.PLUGIN_USER_INFO=t,localStorage.setItem("plugin_user_info",JSON.stringify(t))),window.dispatchEvent(new CustomEvent("pluginSettingsReady",{detail:{cityData:e,userInfo:t}})),console.log("📝 新标签页状态同步完成"),{success:!0,injected:!!e}}catch(e){return console.error("❌ 注入过程中发生错误:",e),{success:!1,error:e.message}}},args:[o.selectedCity||null,a.auth_user_info||null]});if(c&&c[0]&&c[0].result){const{success:t,injected:o}=c[0].result;if(t&&o)return console.log("✅ 设置注入成功验证:",e),!0}t<3&&(console.log(`🔄 注入验证失败，进行第${t+1}次重试:`,e),setTimeout(()=>{injectPluginStatusToTab(e,t+1)},100*(t+1)))}catch(o){o.message.includes("Cannot access")&&t<3?(console.log(`🔄 权限错误，进行第${t+1}次重试:`,e),setTimeout(()=>{injectPluginStatusToTab(e,t+1)},200*(t+1))):console.log("⚠️ 无法向标签页注入状态:",e,o.message)}}chrome.storage.onChanged.addListener((e,t)=>{"sync"===t&&e.selectedCity&&(console.log("🔄 Region settings changed, syncing to all tabs"),chrome.tabs.query({},tabs=>{const t=e.selectedCity.newValue;tabs.forEach(e=>{try{chrome.scripting.executeScript({target:{tabId:e.id},func:(key,e)=>{localStorage.setItem(key,JSON.stringify(e)),console.log("🔄 Updated localStorage with new region settings")},args:["globalTimezoneSpoofer_selectedCity",t]}).catch(()=>{})}catch(e){}})}))});