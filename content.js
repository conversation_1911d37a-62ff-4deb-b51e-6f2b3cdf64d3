!function(){try{const e=JSON.parse(localStorage.getItem("plugin_user_info")||"{}"),t=localStorage.getItem("plugin_last_expiry_check"),now=Date.now();if(t&&now-parseInt(t)<864e5)return;e&&void 0!==e.remaining_days&&e.remaining_days<=0&&(console.log("🚫 浏览器启动检测：用户已过期，清除插件设置"),localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),localStorage.removeItem("plugin_user_info"),localStorage.removeItem("plugin_auth_status"),localStorage.setItem("plugin_expired_handled","true"),"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage({type:"CLEAR_EXPIRED_USER_SETTINGS"}).catch(()=>{})),localStorage.setItem("plugin_last_expiry_check",now.toString())}catch(e){}}(),function(){"use strict";let e=!1,t=!1;function o(){const e=localStorage.getItem("globalTimezoneSpoofer_selectedCity");if(e)try{const t=JSON.parse(e);return console.log("✅ 检测到地区设置:",t.country,t.city),!0}catch(e){return console.log("⚠️ 地区设置解析失败"),!1}return!1}function n(o=!1){if(!function(){const e=window.PLUGIN_USER_INFO||JSON.parse(localStorage.getItem("plugin_user_info")||"{}");return!(e&&e.username&&(e.remaining_days<=0||"expired"===e.status)&&(console.log("🚫 用户账户已过期，自动停用插件功能"),sessionStorage.setItem("plugin_manually_activated","false"),sessionStorage.removeItem("plugin_activation_time"),1))}())return o||console.log("🚫 用户账户已过期，插件功能禁用"),e=!1,!1;const n=function(e=!1){return localStorage.getItem("globalTimezoneSpoofer_selectedCity")?(e||t||console.log("✅ 检测到地区设置，插件自动启用"),!0):(e||t||console.log("⏸️ 未检测到地区设置，插件暂不启用"),!1)}(o);return n?!t&&n?(console.log("🚀 检测到地区设置，自动初始化插件功能..."),e=!0,t=!0,a(),function(){console.log("🎯 插件功能已激活");const e=window.location.hostname.toLowerCase(),t=["github.com","stackoverflow.com","developer.mozilla.org"].some(t=>e.includes(t)),o=e.includes("augment.com")||e.includes("augmentcode.com"),n=t||o;t?console.log("🛡️ 敏感网站检测，部分功能已跳过:",e):o?console.log("🔧 Augment网站检测，启用伪装但跳过问题功能:",e):console.log("🌍 完整伪装已启用:",e);if(navigator.geolocation){navigator.geolocation.getCurrentPosition;console.log("🔧 Pre-overriding geolocation API to prevent interference from other scripts")}const i={"America/New_York":[{city:"New York",state:"NY",country:"United States",country_code:"US",lat:40.7128,lng:-74.006,locale:"en-US"},{city:"Boston",state:"MA",country:"United States",country_code:"US",lat:42.3601,lng:-71.0589,locale:"en-US"},{city:"Philadelphia",state:"PA",country:"United States",country_code:"US",lat:39.9526,lng:-75.1652,locale:"en-US"},{city:"Atlanta",state:"GA",country:"United States",country_code:"US",lat:33.749,lng:-84.388,locale:"en-US"},{city:"Miami",state:"FL",country:"United States",country_code:"US",lat:25.7617,lng:-80.1918,locale:"en-US"},{city:"Washington",state:"DC",country:"United States",country_code:"US",lat:38.9072,lng:-77.0369,locale:"en-US"},{city:"Charlotte",state:"NC",country:"United States",country_code:"US",lat:35.2271,lng:-80.8431,locale:"en-US"},{city:"Jacksonville",state:"FL",country:"United States",country_code:"US",lat:30.3322,lng:-81.6557,locale:"en-US"}],"America/Chicago":[{city:"Chicago",state:"IL",country:"United States",country_code:"US",lat:41.8781,lng:-87.6298,locale:"en-US"},{city:"Houston",state:"TX",country:"United States",country_code:"US",lat:29.7604,lng:-95.3698,locale:"en-US"},{city:"Dallas",state:"TX",country:"United States",country_code:"US",lat:32.7767,lng:-96.797,locale:"en-US"},{city:"San Antonio",state:"TX",country:"United States",country_code:"US",lat:29.4241,lng:-98.4936,locale:"en-US"},{city:"Austin",state:"TX",country:"United States",country_code:"US",lat:30.2672,lng:-97.7431,locale:"en-US"},{city:"Minneapolis",state:"MN",country:"United States",country_code:"US",lat:44.9778,lng:-93.265,locale:"en-US"},{city:"Kansas City",state:"MO",country:"United States",country_code:"US",lat:39.0997,lng:-94.5786,locale:"en-US"},{city:"Nashville",state:"TN",country:"United States",country_code:"US",lat:36.1627,lng:-86.7816,locale:"en-US"}],"America/Denver":[{city:"Denver",state:"CO",country:"United States",country_code:"US",lat:39.7392,lng:-104.9903,locale:"en-US"},{city:"Phoenix",state:"AZ",country:"United States",country_code:"US",lat:33.4484,lng:-112.074,locale:"en-US"},{city:"Salt Lake City",state:"UT",country:"United States",country_code:"US",lat:40.7608,lng:-111.891,locale:"en-US"},{city:"Albuquerque",state:"NM",country:"United States",country_code:"US",lat:35.0844,lng:-106.6504,locale:"en-US"},{city:"Colorado Springs",state:"CO",country:"United States",country_code:"US",lat:38.8339,lng:-104.8214,locale:"en-US"},{city:"Mesa",state:"AZ",country:"United States",country_code:"US",lat:33.4152,lng:-111.8315,locale:"en-US"},{city:"Tucson",state:"AZ",country:"United States",country_code:"US",lat:32.2226,lng:-110.9747,locale:"en-US"}],"America/Los_Angeles":[{city:"Los Angeles",state:"CA",country:"United States",country_code:"US",lat:34.0522,lng:-118.2437,locale:"en-US"},{city:"San Francisco",state:"CA",country:"United States",country_code:"US",lat:37.7749,lng:-122.4194,locale:"en-US"},{city:"Seattle",state:"WA",country:"United States",country_code:"US",lat:47.6062,lng:-122.3321,locale:"en-US"},{city:"San Diego",state:"CA",country:"United States",country_code:"US",lat:32.7157,lng:-117.1611,locale:"en-US"},{city:"Las Vegas",state:"NV",country:"United States",country_code:"US",lat:36.1699,lng:-115.1398,locale:"en-US"},{city:"Portland",state:"OR",country:"United States",country_code:"US",lat:45.5152,lng:-122.6784,locale:"en-US"},{city:"Sacramento",state:"CA",country:"United States",country_code:"US",lat:38.5816,lng:-121.4944,locale:"en-US"},{city:"San Jose",state:"CA",country:"United States",country_code:"US",lat:37.3382,lng:-121.8863,locale:"en-US"}],"Asia/Taipei":[{city:"Taipei",state:"Taipei City",country:"Taiwan",country_code:"TW",lat:25.033,lng:121.5654,locale:"zh-TW"},{city:"Kaohsiung",state:"Kaohsiung City",country:"Taiwan",country_code:"TW",lat:22.6273,lng:120.3014,locale:"zh-TW"},{city:"Taichung",state:"Taichung City",country:"Taiwan",country_code:"TW",lat:24.1477,lng:120.6736,locale:"zh-TW"},{city:"Tainan",state:"Tainan City",country:"Taiwan",country_code:"TW",lat:22.9999,lng:120.2269,locale:"zh-TW"},{city:"Taoyuan",state:"Taoyuan City",country:"Taiwan",country_code:"TW",lat:24.9936,lng:121.301,locale:"zh-TW"}],"Asia/Tokyo":[{city:"Tokyo",state:"Tokyo",country:"Japan",country_code:"JP",lat:35.6762,lng:139.6503,locale:"ja-JP"},{city:"Osaka",state:"Osaka",country:"Japan",country_code:"JP",lat:34.6937,lng:135.5023,locale:"ja-JP"},{city:"Yokohama",state:"Kanagawa",country:"Japan",country_code:"JP",lat:35.4437,lng:139.638,locale:"ja-JP"},{city:"Nagoya",state:"Aichi",country:"Japan",country_code:"JP",lat:35.1815,lng:136.9066,locale:"ja-JP"},{city:"Sapporo",state:"Hokkaido",country:"Japan",country_code:"JP",lat:43.0642,lng:141.3469,locale:"ja-JP"},{city:"Fukuoka",state:"Fukuoka",country:"Japan",country_code:"JP",lat:33.5904,lng:130.4017,locale:"ja-JP"},{city:"Kobe",state:"Hyogo",country:"Japan",country_code:"JP",lat:34.6901,lng:135.1956,locale:"ja-JP"},{city:"Kyoto",state:"Kyoto",country:"Japan",country_code:"JP",lat:35.0116,lng:135.7681,locale:"ja-JP"}],"Asia/Singapore":[{city:"Singapore",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3521,lng:103.8198,locale:"en-SG"},{city:"Jurong West",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3404,lng:103.709,locale:"en-SG"},{city:"Woodlands",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.4382,lng:103.789,locale:"en-SG"},{city:"Tampines",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3496,lng:103.9568,locale:"en-SG"},{city:"Sengkang",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3868,lng:103.8914,locale:"en-SG"}]};let a,r;const c="globalTimezoneSpoofer_selectedCity",l="globalTimezoneSpoofer_fingerprint",s=localStorage.getItem(c);let u,g=localStorage.getItem(l);if(s)try{r=JSON.parse(s),a=r.timezone,console.log("🎯 Using saved region settings:",r.country,r.city)}catch(e){console.log("⚠️ Failed to parse saved settings, using random selection"),r=null}if(!r){const e=Object.keys(i);a=e[Math.floor(Math.random()*e.length)];const t=i[a];r=t[Math.floor(Math.random()*t.length)],console.log("🎲 Using random region settings (no saved preference)")}if(g)try{u=JSON.parse(g),console.log("🔒 Using saved fingerprint data")}catch(e){u=null}u||(u={latOffset:.2*(Math.random()-.5),lngOffset:.2*(Math.random()-.5),accuracyVariation:Math.floor(50*Math.random())+20,deviceMemory:[4,8,16,32][Math.floor(4*Math.random())],webglVendor:["Intel Inc.","NVIDIA Corporation","AMD","Apple Inc."][Math.floor(4*Math.random())],webglRenderer:["Intel Iris OpenGL Engine","NVIDIA GeForce GTX 1060","AMD Radeon Pro 560","Apple M1","Intel HD Graphics 620"][Math.floor(5*Math.random())],webrtcIPRange:["8.8.8.","8.8.4.","1.1.1.","1.0.0.","208.67.222.","208.67.220.","4.2.2.","4.2.1.","173.252.","31.13.","172.217.","142.250."][Math.floor(10*Math.random())],webrtcLastOctet:Math.floor(254*Math.random())+1},localStorage.setItem(l,JSON.stringify(u)),console.log("🎲 Generated and saved new fingerprint data"));const d=u.latOffset,p=u.lngOffset,y=u.accuracyVariation;function m(e){try{const now=new Date,t=now.getTime()+6e4*now.getTimezoneOffset(),o=new Intl.DateTimeFormat("en",{timeZone:e,timeZoneName:"longOffset"}),n=o.formatToParts(now).find(e=>"timeZoneName"===e.type);if(n&&n.value){const e=n.value.match(/GMT([+-])(\d{1,2})(?::?(\d{2}))?/);if(e){const t="+"===e[1]?1:-1,o=parseInt(e[2]);return t*(60*o+parseInt(e[3]||"0"))}}const i=(new Date(now.toLocaleString("en-US",{timeZone:e})).getTime()-t)/6e4;return Math.round(i)}catch(t){console.warn("Failed to calculate timezone offset for",e,t);return{"America/New_York":-300,"America/Chicago":-360,"America/Denver":-420,"America/Los_Angeles":-480,"America/Phoenix":-420,"America/Indiana/Indianapolis":-300,"America/Detroit":-300,"Asia/Tokyo":540,"Asia/Taipei":480,"Asia/Singapore":480}[e]||0}}const f=m(a);function h(){const e=new Date,t=m(a),o=e.getTimezoneOffset();return console.log("🔍 Timezone Offset Validation:"),console.log("  - Calculated offset:",t),console.log("  - Date.getTimezoneOffset():",o),console.log("  - Match:",t===o?"✅":"❌"),t}const S={timezone:a,timezone_name:a.split("/")[1].replace("_"," ")+" Time",latitude:parseFloat((r.lat+d).toFixed(6)),longitude:parseFloat((r.lng+p).toFixed(6)),city:r.city,state:r.state,country:r.country,country_code:r.country_code,locale:r.locale,accuracy:y,offset:-f/60,dst:!0};function w(e,t,o){const n=new v(t-60*o*60*1e3);return`${e}(GMT)Local time: ${`${n.getFullYear()}-${String(n.getMonth()+1).padStart(2,"0")}-${String(n.getDate()).padStart(2,"0")} ${String(n.getHours()).padStart(2,"0")}:${String(n.getMinutes()).padStart(2,"0")}:${String(n.getSeconds()).padStart(2,"0")}`}`}const T=Intl.DateTimeFormat.prototype.resolvedOptions;Intl.DateTimeFormat.prototype.resolvedOptions=function(){const e=T.call(this);return e.timeZone=S.timezone,e.locale=S.locale,e};Date.prototype.getTimezoneOffset;Date.prototype.getTimezoneOffset=function(){return f};const v=Date,C=Date.prototype.toString,U=Date.prototype.toDateString,P=Date.prototype.toTimeString,b=Date.prototype.toLocaleString;Date.prototype.toString=function(){const e=this.getTime(),t=f,o=new v(e-60*t*1e3),n=w(a,e,t/60);return C.call(o).replace(/GMT[+-]\d{4}.*$/,n)},Date.prototype.toDateString=function(){const e=this.getTime(),t=new v(e-60*f*1e3);return U.call(t)},Date.prototype.toTimeString=function(){const e=this.getTime(),t=f,o=new v(e-60*t*1e3),n=w(a,e,t/60);return P.call(o).replace(/GMT[+-]\d{4}.*$/,n)},Date.prototype.toLocaleString=function(e,t){(t=t||{}).timeZone=S.timezone;const o=e||S.locale,n=b.call(this,o,t);return console.log("🕐 toLocaleString called:",n,"TimeZone:",t.timeZone,"Locale:",o),n};const _=window.Date;window.Date=function(...e){let t;t=0===e.length?new _:new _(...e),t.getTimezoneOffset=function(){return f},t.toString=Date.prototype.toString,t.toDateString=Date.prototype.toDateString,t.toTimeString=Date.prototype.toTimeString,t.toLocaleString=Date.prototype.toLocaleString,t.toLocaleDateString=Date.prototype.toLocaleDateString,t.toLocaleTimeString=Date.prototype.toLocaleTimeString;t.getHours,t.getMinutes,t.getSeconds;return t.getHours=function(){const e=this.getTime();return new _(e-60*f*1e3).getHours()},t.getMinutes=function(){const e=this.getTime();return new _(e-60*f*1e3).getMinutes()},t.getSeconds=function(){const e=this.getTime();return new _(e-60*f*1e3).getSeconds()},t},Object.setPrototypeOf(window.Date,_),Object.setPrototypeOf(window.Date.prototype,_.prototype),window.Date.now=_.now,window.Date.parse=_.parse,window.Date.UTC=_.UTC;const L=Intl.DateTimeFormat;Intl.DateTimeFormat=function(e,t){return(t=t||{}).timeZone=S.timezone,e=e||S.locale,new L(e,t)},Intl.DateTimeFormat.prototype=L.prototype,Intl.DateTimeFormat.supportedLocalesOf=L.supportedLocalesOf;const I=Date.prototype.toLocaleDateString;Date.prototype.toLocaleDateString=function(e,t){(t=t||{}).timeZone=S.timezone;const o=e||S.locale;return I.call(this,o,t)};const z=Date.prototype.toLocaleTimeString;if(Date.prototype.toLocaleTimeString=function(e,t){(t=t||{}).timeZone=S.timezone;const o=e||S.locale;return z.call(this,o,t)},"undefined"!=typeof Temporal&&Temporal.Now){Temporal.Now.timeZone;Temporal.Now.timeZone=function(){return Temporal.TimeZone.from(S.timezone)}}const O=.01*(Math.random()-.5),A=.01*(Math.random()-.5),M=parseFloat((S.latitude+O).toFixed(6)),D=parseFloat((S.longitude+A).toFixed(6));console.log("🎯 Fixed coordinates generated:",M,D,"(no jumping)");const k={getCurrentPosition:function(e,t,o){if(e){const t=M,o=D,n={coords:{latitude:t,longitude:o,accuracy:S.accuracy+y,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()};console.log("🎯 Returning US coordinates:",t,o,"City:",S.city),setTimeout(()=>e(n),100)}},watchPosition:function(e,t,o){if(e){const t={coords:{latitude:M,longitude:D,accuracy:S.accuracy+Math.floor(20*Math.random())-10,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()};setTimeout(()=>e(t),100)}return Math.floor(1e3*Math.random())+1},clearWatch:function(e){}};try{console.log("🔧 Skipping direct assignment method (navigator.geolocation is read-only)");try{try{delete navigator.geolocation}catch(e){}Object.defineProperty(navigator,"geolocation",{get:function(){return console.log("🎯 Geolocation API called, returning US coordinates:",M,D),k},set:function(e){console.log("🛡️ Blocking external geolocation reset")},configurable:!0,enumerable:!0}),console.log("✅ Method 2: defineProperty override successful")}catch(e){console.warn("⚠️ Method 2 failed:",e.message)}try{navigator.geolocation&&"object"==typeof navigator.geolocation&&(navigator.geolocation.getCurrentPosition=k.getCurrentPosition,navigator.geolocation.watchPosition=k.watchPosition,navigator.geolocation.clearWatch=k.clearWatch,console.log("✅ Method 3: Prototype method override successful"))}catch(e){console.warn("⚠️ Method 3 failed:",e.message)}try{if(window.Geolocation&&window.Geolocation.prototype){window.Geolocation.prototype.getCurrentPosition,window.Geolocation.prototype.watchPosition;window.Geolocation.prototype.getCurrentPosition=k.getCurrentPosition,window.Geolocation.prototype.watchPosition=k.watchPosition,console.log("✅ Method 4: Global Geolocation prototype override successful")}}catch(e){console.warn("⚠️ Method 4 failed:",e.message)}console.log("✅ Geolocation override applied, current coordinates:",S.latitude,S.longitude),setTimeout(()=>{try{navigator.geolocation&&navigator.geolocation.getCurrentPosition&&navigator.geolocation.getCurrentPosition(e=>{console.log("🧪 Geolocation test result:",e.coords.latitude,e.coords.longitude),35.664===e.coords.latitude||139.6982===e.coords.longitude?console.error("❌ Warning: Geolocation override failed, still showing Japanese coordinates!"):console.log("✅ Geolocation override successful, showing US coordinates")},e=>{console.log("🧪 Geolocation test error:",e.message)})}catch(e){console.warn("⚠️ Geolocation test failed:",e.message)}},1e3)}catch(e){console.error("❌ Geolocation override critical failure:",e.message,e.stack)}function R(e,t){const o={"en-US":[["en-US","en"],["en-US","en","es"],["en-US","en","fr"],["en-US"]],"zh-TW":[["zh-TW","zh","en"],["zh-TW","zh"],["zh-TW","en"],["zh-TW"]],"ja-JP":[["ja-JP","ja","en"],["ja-JP","ja"],["ja","en"],["ja-JP"]],"en-SG":[["en-SG","en","zh"],["en-SG","en"],["en","zh"],["en-SG"]]}[e]||[["en-US","en"]];return o[Math.floor(Math.random()*o.length)]}const x=R(S.locale,S.country_code);try{Object.defineProperty(navigator,"language",{get:function(){return x[0]},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.language:",e.message)}try{Object.defineProperty(navigator,"languages",{get:function(){return x},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.languages:",e.message)}const j=[{width:1920,height:1080},{width:1366,height:768},{width:1440,height:900},{width:1536,height:864},{width:1280,height:720},{width:2560,height:1440},{width:1600,height:900}],G=j[Math.floor(Math.random()*j.length)];Object.defineProperty(screen,"width",{get:function(){return G.width}}),Object.defineProperty(screen,"height",{get:function(){return G.height}}),Object.defineProperty(screen,"availWidth",{get:function(){return G.width}}),Object.defineProperty(screen,"availHeight",{get:function(){return G.height-40}});const F=[4,6,8,12,16][Math.floor(5*Math.random())];try{Object.defineProperty(navigator,"hardwareConcurrency",{get:function(){return F},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.hardwareConcurrency:",e.message)}if("deviceMemory"in navigator)try{Object.defineProperty(navigator,"deviceMemory",{get:function(){return u.deviceMemory},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.deviceMemory:",e.message)}if("connection"in navigator){const e=["4g","wifi","ethernet"],t=e[Math.floor(Math.random()*e.length)];try{Object.defineProperty(navigator.connection,"effectiveType",{get:function(){return t},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.connection.effectiveType:",e.message)}}console.log("🌍 Global Timezone & Location Spoofer v2.9.5 Active"),console.log("📍 Selected Location:",S.city+", "+S.state+", "+S.country),console.log("🕐 Selected Timezone:",S.timezone+" ("+S.timezone_name+")"),console.log("🌐 Country Code:",S.country_code+" | Locale:",S.locale),console.log("📊 Coordinates:",S.latitude+", "+S.longitude),console.log("⏰ Timezone Offset:",f+" minutes (UTC"+(f>0?"+":"")+-f/60+")"),console.log("🔧 Offset Calculation: getTimezoneOffset() returns",f,"for timezone",a),setTimeout(()=>{h()},500),console.log("🖥️ Resolution:",G.width+"x"+G.height),console.log("💻 CPU Cores:",F),console.log("🗣️ Languages:",x.join(", ")),console.log("🎯 Accuracy Variation:",S.accuracy+"±10 meters"),console.log("🎛️ User region selection: Click extension icon to choose region"),console.log("🔄 Persistent settings: Same region across all pages"),console.log("🕐 Current time test:",(new Date).toString()),console.log("👨‍💻 Author: XiaoYuYouShui | 🌐 Website: https://xoxome.online"),console.log("🌍 Current website:",e,"- Sensitive site:",t,"- Augment site:",o);const N=()=>{document.querySelectorAll('[style*="display:none"], [style*="visibility:hidden"], .hidden').forEach(e=>{(e.textContent.includes("timezone")||e.textContent.includes("location")||e.textContent.includes("country"))&&(console.log("🕵️ Detected potential antifraud test element:",e),e.textContent.includes("timezone")&&(e.textContent=e.textContent.replace(/timezone[^a-zA-Z]*[a-zA-Z_\/]+/gi,`timezone: ${S.timezone}`)),e.textContent.includes("country")&&(e.textContent=e.textContent.replace(/country[^a-zA-Z]*[a-zA-Z]+/gi,`country: ${S.country_code}`)))});document.querySelectorAll("script").forEach(e=>{(e.textContent.includes("getTimezoneOffset")||e.textContent.includes("Intl.DateTimeFormat"))&&console.log("🕵️ Detected script with timezone detection:",e.src||"inline")})};if(setTimeout(N,1e3),setTimeout(N,3e3),setTimeout(()=>{console.log("🔍 === 插件状态检查 ==="),console.log("🌍 地理位置:",S.city,S.state),console.log("🕐 时区:",S.timezone),console.log("📍 坐标:",M,D),console.log("🌐 用户代理:",navigator.userAgent),console.log("⏰ 时区偏移:",(new Date).getTimezoneOffset()),console.log("🗓️ 本地时间:",(new Date).toLocaleString()),console.log("🌏 Intl时区:",Intl.DateTimeFormat().resolvedOptions().timeZone),navigator.geolocation&&navigator.geolocation.getCurrentPosition(e=>console.log("📍 地理位置测试:",e.coords.latitude,e.coords.longitude),e=>console.log("❌ 地理位置错误:",e.message))},2e3),t)console.log("🛡️ Skipping timer interception to protect website functionality");else{const e=window.setInterval,t=window.setTimeout;window.setInterval=function(t,o,...n){if("function"==typeof t){const i=function(){return t.apply(this,arguments)};return e.call(this,i,o,...n)}return e.call(this,t,o,...n)},window.setTimeout=function(e,o,...n){if("function"==typeof e){const i=function(){return e.apply(this,arguments)};return t.call(this,i,o,...n)}return t.call(this,e,o,...n)}}o&&console.log("⏱️ Skipping Performance API modification for Augment site to avoid Cloudflare conflicts");const W=window.requestAnimationFrame;window.requestAnimationFrame=function(e){const t=function(t){return e.call(this,t)};return W.call(this,t)};const E=Number.prototype.toLocaleString;Number.prototype.toLocaleString=function(e,t){const o=e||S.locale;return E.call(this,o,t)};const J=Intl.NumberFormat;Intl.NumberFormat=function(e,t){return new J("en-US",t)},Intl.NumberFormat.prototype=J.prototype,Intl.NumberFormat.supportedLocalesOf=J.supportedLocalesOf;const H=Intl.Collator;if(Intl.Collator=function(e,t){return new H("en-US",t)},Intl.Collator.prototype=H.prototype,Intl.Collator.supportedLocalesOf=H.supportedLocalesOf,void 0!==Intl.RelativeTimeFormat){const e=Intl.RelativeTimeFormat;Intl.RelativeTimeFormat=function(t,o){return new e("en-US",o)},Intl.RelativeTimeFormat.prototype=e.prototype,Intl.RelativeTimeFormat.supportedLocalesOf=e.supportedLocalesOf}if(void 0!==Intl.PluralRules){const e=Intl.PluralRules;Intl.PluralRules=function(t,o){return new e("en-US",o)},Intl.PluralRules.prototype=e.prototype,Intl.PluralRules.supportedLocalesOf=e.supportedLocalesOf}if(n)o?console.log("🔧 Skipping network request interception for Augment site compatibility"):console.log("🛡️ Skipping network request interception to protect website functionality");else{const e=window.fetch;window.fetch=function(input,t){const o="string"==typeof input?input:input.url;if(o&&o.includes("verisoul.ai"))return console.log("🛡️ Intercepting Verisoul anti-fraud request:",o),Promise.resolve(new Response(JSON.stringify({success:!0,status:"verified",country:S.country_code,region:S.state,city:S.city,timezone:S.timezone,risk_score:.1,verification_status:"passed",device_trust:"high",location_trust:"verified"}),{status:200,headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}}));if(o&&["ipapi.co","ip-api.com","ipinfo.io","geoip","ipgeolocation","freegeoip","cloudflare","maxmind","location","geo","country","region","whatismyip","myip","checkip","getip","ipecho","icanhazip","ident.me","ifconfig.me","httpbin.org/ip"].some(e=>o.includes(e)))return console.log("🚫 Intercepting IP/location detection request:",o),Promise.resolve(new Response(JSON.stringify({country:S.country,country_code:S.country_code,country_name:S.country,region:S.state,region_code:S.state.substring(0,2).toUpperCase(),city:S.city,latitude:M,longitude:D,timezone:S.timezone,timezone_name:S.timezone_name,ip:"*******",status:"success",message:"success"}),{status:200,headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"}}));if(t&&t.headers){const e=new Headers(t.headers);e.delete("CF-IPCountry"),e.delete("X-Forwarded-For"),e.delete("X-Real-IP"),e.delete("X-Country-Code"),e.delete("CF-Connecting-IP"),e.delete("X-Geo-Country"),e.set("Accept-Language","en-US,en;q=0.9"),t.headers=e}else t?t.headers={"Accept-Language":"en-US,en;q=0.9"}:t={headers:{"Accept-Language":"en-US,en;q=0.9"}};return e.call(this,input,t)};const t=XMLHttpRequest.prototype.open,o=XMLHttpRequest.prototype.setRequestHeader;XMLHttpRequest.prototype.open=function(e,o){const n=t.apply(this,arguments);if(o&&(o.includes("geo")||o.includes("location")||o.includes("ip")||o.includes("country")))try{this.setRequestHeader("Accept-Language","en-US,en;q=0.9")}catch(e){}return n},XMLHttpRequest.prototype.setRequestHeader=function(e,t){return"accept-language"===e.toLowerCase()&&(t="en-US,en;q=0.9"),o.call(this,e,t)}}if(o)console.log("🎮 Skipping WebGL fingerprint modification for Augment site to avoid Cloudflare conflicts");else{const e=document.createElement("canvas"),t=e.getContext("webgl")||e.getContext("experimental-webgl");if(t){const e=t.getParameter;t.getParameter=function(o){return o===t.VENDOR?u.webglVendor:o===t.RENDERER?u.webglRenderer:e.call(this,o)}}}if(o)console.log("🎨 Skipping Canvas fingerprint modification for Augment site to avoid Cloudflare conflicts");else{const e=HTMLCanvasElement.prototype.toDataURL;HTMLCanvasElement.prototype.toDataURL=function(){const t=this.getContext("2d");if(t){const e=t.getImageData(0,0,this.width,this.height);for(let t=0;t<e.data.length;t+=4)t%1e3==0&&(e.data[t]=Math.min(255,e.data[t]+1));t.putImageData(e,0,0)}return e.apply(this,arguments)}}if("undefined"==typeof AudioContext||o)o&&console.log("🔊 Skipping AudioContext fingerprint modification for Augment site to avoid Cloudflare conflicts");else{const e=AudioContext.prototype.createAnalyser;AudioContext.prototype.createAnalyser=function(){const t=e.call(this),o=t.getFloatFrequencyData;return t.getFloatFrequencyData=function(e){o.call(this,e);for(let t=0;t<e.length;t++)e[t]+=1e-4*(Math.random()-.5)},t}}if(o)console.log("🔤 Skipping font fingerprint modification for Augment site to avoid Cloudflare conflicts");else{const e=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"offsetWidth"),t=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"offsetHeight");Object.defineProperty(HTMLElement.prototype,"offsetWidth",{get:function(){const t=e.get.call(this);return this.style&&this.style.fontFamily?t+(Math.random()<.5?0:1):t}}),Object.defineProperty(HTMLElement.prototype,"offsetHeight",{get:function(){const e=t.get.call(this);return this.style&&this.style.fontFamily?e+(Math.random()<.5?0:1):e}})}if("undefined"!=typeof performance&&performance.getEntriesByType&&!o){const e=performance.getEntriesByType;performance.getEntriesByType=function(type){return e.call(this,type).filter(e=>{if(e.name){const t=e.name.toLowerCase();return!(t.includes("geoip")||t.includes("location")||t.includes("country")||t.includes("region"))}return!0})}}if(void 0!==window.matchMedia){const e=window.matchMedia;window.matchMedia=function(query){return query.includes("prefers-color-scheme")?{matches:query.includes("light"),media:query,addListener:function(){},removeListener:function(){},addEventListener:function(){},removeEventListener:function(){},dispatchEvent:function(){return!0}}:e.call(this,query)}}try{Object.defineProperty(navigator,"systemLanguage",{get:function(){return"en-US"},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.systemLanguage:",e.message)}try{Object.defineProperty(navigator,"userLanguage",{get:function(){return"en-US"},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.userLanguage:",e.message)}try{Object.defineProperty(navigator,"browserLanguage",{get:function(){return"en-US"},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.browserLanguage:",e.message)}try{Object.defineProperty(navigator,"platform",{get:function(){const e=["Win32","MacIntel","Linux x86_64"];return e[Math.floor(Math.random()*e.length)]},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.platform:",e.message)}try{Object.defineProperty(navigator,"userAgent",{get:function(){return"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"},configurable:!0}),Object.defineProperty(navigator,"appVersion",{get:function(){return"5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"},configurable:!0}),Object.defineProperty(navigator,"vendor",{get:function(){return"Google Inc."},configurable:!0}),console.log("✅ User agent and vendor information overridden"),console.log("🔍 Current User Agent:",navigator.userAgent),console.log("🔍 Current App Version:",navigator.appVersion),console.log("🔍 Current Vendor:",navigator.vendor)}catch(e){console.warn("⚠️ Cannot override navigator userAgent/vendor:",e.message)}const q=Object.getOwnPropertyDescriptor(Document.prototype,"cookie").get,Z=Object.getOwnPropertyDescriptor(Document.prototype,"cookie").set;function X(){if(window.RTCPeerConnection){const e=window.RTCPeerConnection;window.RTCPeerConnection=function(t){const o=u.webrtcIPRange+u.webrtcLastOctet,n={...t,iceServers:[],iceCandidatePoolSize:0},i=new e(n),a=i.onicecandidate;i.onicecandidate=function(e){if(e.candidate){const t={...e.candidate,candidate:`candidate:1 1 UDP 2130706431 ${o} 54400 typ host generation 0`,address:o,ip:o},n={...e,candidate:t};console.log("🔒 WebRTC IP masked:",o),a&&a.call(this,n)}else a&&a.call(this,e)};const r=i.addEventListener;return i.addEventListener=function(type,e,t){if("icecandidate"===type){const n=function(t){if(t.candidate){const n={...t.candidate,candidate:`candidate:1 1 UDP 2130706431 ${o} 54400 typ host generation 0`,address:o,ip:o},i={...t,candidate:n};e.call(this,i)}else e.call(this,t)};return r.call(this,type,n,t)}return r.call(this,type,e,t)},console.log("🔒 WebRTC completely intercepted, IP masked as:",o),i},window.RTCPeerConnection.prototype=e.prototype,window.webkitRTCPeerConnection&&(window.webkitRTCPeerConnection=window.RTCPeerConnection),window.mozRTCPeerConnection&&(window.mozRTCPeerConnection=window.RTCPeerConnection),console.log("✅ WebRTC interception setup completed")}else console.warn("⚠️ RTCPeerConnection not available, WebRTC interception skipped")}Object.defineProperty(document,"cookie",{get:function(){let e=q.call(this);return e=e.replace(/[^;]*(?:country|region|location|timezone|geoip)[^;]*;?\s*/gi,""),e},set:function(e){if(!/(?:country|region|location|timezone|geoip)/i.test(e))return Z.call(this,e)}}),X(),"loading"===document.readyState&&document.addEventListener("DOMContentLoaded",X);window.addEventListener("load",X);const $=Object.defineProperty;Object.defineProperty=function(e,t,o){if(e===window&&("RTCPeerConnection"===t||"webkitRTCPeerConnection"===t||"mozRTCPeerConnection"===t)){console.log("🛡️ Detected attempt to redefine WebRTC, re-applying interception");const n=$.call(this,e,t,o);return setTimeout(X,0),n}return $.call(this,e,t,o)};let V=0;const K=setInterval(()=>{V++,V>5?clearInterval(K):window.RTCPeerConnection&&-1===window.RTCPeerConnection.toString().indexOf("fakeIP")&&(console.log("🔄 WebRTC interception lost, re-applying..."),X())},1e3);if(navigator.connection)try{Object.defineProperty(navigator.connection,"effectiveType",{get:function(){return"4g"},configurable:!0}),Object.defineProperty(navigator.connection,"downlink",{get:function(){return 10},configurable:!0}),Object.defineProperty(navigator.connection,"rtt",{get:function(){return 50},configurable:!0})}catch(e){console.warn("⚠️ Cannot override navigator.connection properties:",e.message)}if(window.performance&&window.performance.now&&!o){const e=window.performance.now;e.call(window.performance);if(window.performance.now=function(){return e.call(window.performance)},window.performance.timing){const e=window.performance.timing,t=new Proxy(e,{get:function(target,e){const t=target[e];return t}});Object.defineProperty(window.performance,"timing",{get:function(){return t},configurable:!0})}}const Y=localStorage.getItem,B=localStorage.setItem,Q=sessionStorage.getItem,ee=sessionStorage.setItem;localStorage.getItem=function(key){const e=Y.call(this,key);if("globalTimezoneSpoofer_selectedCity"===key)return e;if(key&&/(?:country|region|location|timezone|geoip|lat|lng|city)/i.test(key)){if(key.toLowerCase().includes("country"))return S.country_code;if(key.toLowerCase().includes("timezone"))return S.timezone;if(key.toLowerCase().includes("city"))return S.city;if(key.toLowerCase().includes("region"))return S.state}return e},localStorage.setItem=function(key,e){return"globalTimezoneSpoofer_selectedCity"===key||key&&/(?:country|region|location|timezone|geoip)/i.test(key)&&(key.toLowerCase().includes("country")&&(e=S.country_code),key.toLowerCase().includes("timezone")&&(e=S.timezone),key.toLowerCase().includes("city")&&(e=S.city),key.toLowerCase().includes("region")&&(e=S.state)),B.call(this,key,e)},sessionStorage.getItem=function(key){const e=Q.call(this,key);if(key&&/(?:country|region|location|timezone|geoip|lat|lng|city)/i.test(key)){if(key.toLowerCase().includes("country"))return S.country_code;if(key.toLowerCase().includes("timezone"))return S.timezone;if(key.toLowerCase().includes("city"))return S.city;if(key.toLowerCase().includes("region"))return S.state}return e},sessionStorage.setItem=function(key,e){return key&&/(?:country|region|location|timezone|geoip)/i.test(key)&&(key.toLowerCase().includes("country")&&(e=S.country_code),key.toLowerCase().includes("timezone")&&(e=S.timezone),key.toLowerCase().includes("city")&&(e=S.city),key.toLowerCase().includes("region")&&(e=S.state)),ee.call(this,key,e)};const te=history.pushState,oe=history.replaceState;history.pushState=function(e,t,o){return o&&"string"==typeof o&&(o=o.replace(/[?&](?:country|region|location|timezone|geoip|lat|lng)=[^&]*/gi,"")),te.call(this,e,t,o)},history.replaceState=function(e,t,o){return o&&"string"==typeof o&&(o=o.replace(/[?&](?:country|region|location|timezone|geoip|lat|lng)=[^&]*/gi,"")),oe.call(this,e,t,o)};const ne=window.WebSocket;ne&&(window.WebSocket=function(e,t){const o=new ne(e,t),n=o.send;return o.send=function(e){return"string"==typeof e&&(e=(e=(e=e.replace(/"country":\s*"[^"]*"/gi,`"country":"${S.country_code}"`)).replace(/"timezone":\s*"[^"]*"/gi,`"timezone":"${S.timezone}"`)).replace(/"region":\s*"[^"]*"/gi,`"region":"${S.state}"`)),n.call(this,e)},o},window.WebSocket.prototype=ne.prototype)}(),!0):e:(e=!1,!1)}let i=!1;function a(){if(!i)return;i=!1,console.log("✅ 解除页面显示阻塞，插件初始化完成");const e=document.getElementById("plugin-loading-overlay");e&&(e.style.opacity="0",e.style.transition="opacity 0.3s ease",setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},300))}!function(){console.log("🌍 Global Timezone & Location Spoofer 已加载"),window.addEventListener("pluginSettingsReady",e=>{console.log("📢 收到设置就绪通知，立即初始化插件");const{cityData:o}=e.detail;o&&!t&&setTimeout(()=>{n()&&console.log("✅ 通过事件通知成功初始化插件")},10)});o()||(!function(){if(i)return;i=!0,console.log("🚫 隐藏页面内容，等待插件初始化完成...");const e=document.createElement("div");if(e.id="plugin-loading-overlay",e.style.cssText="\n            position: fixed !important;\n            top: 0 !important;\n            left: 0 !important;\n            width: 100vw !important;\n            height: 100vh !important;\n            background: #f8f9fa !important;\n            z-index: 999999 !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            font-family: Arial, sans-serif !important;\n            color: #333 !important;\n        ",e.innerHTML='\n            <div style="text-align: center; padding: 20px;">\n                <div style="font-size: 24px; margin-bottom: 15px;">🌍</div>\n                <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">\n                    Global Timezone & Location Spoofer\n                </div>\n                <div style="font-size: 14px; color: #666; margin-bottom: 15px;">\n                    正在初始化插件功能，请稍候...\n                </div>\n                <div style="width: 200px; height: 4px; background: #e9ecef; border-radius: 2px; overflow: hidden;">\n                    <div style="width: 100%; height: 100%; background: linear-gradient(90deg, #007bff, #28a745); animation: loading 2s infinite;"></div>\n                </div>\n                <style>\n                    @keyframes loading {\n                        0% { transform: translateX(-100%); }\n                        100% { transform: translateX(100%); }\n                    }\n                </style>\n            </div>\n        ',document.documentElement)document.documentElement.appendChild(e);else{const t=new MutationObserver(()=>{document.documentElement&&(document.documentElement.appendChild(e),t.disconnect())});t.observe(document,{childList:!0})}setTimeout(()=>{i&&!t&&(console.log("⏰ 第一层超时，检查是否有设置注入"),o()?(console.log("🔄 发现设置，尝试初始化"),n()):console.log("⚠️ 仍无设置，继续等待"))},1e3),setTimeout(()=>{i&&(console.log("⏰ 最终超时，强制解除页面阻塞"),a())},3e3)}(),setTimeout(()=>{o()&&(console.log("🔄 延迟检查发现地区设置，尝试初始化"),n())},200));if(n())return a(),void console.log("🚀 插件已在页面加载前完成初始化");!function(){let e=0;const o=setInterval(()=>{e++;n(t||e>2)&&(clearInterval(o),console.log("✅ 插件已激活，停止定期检查"),a()),t&&clearInterval(o)},1e3);setTimeout(()=>{clearInterval(o),t||(console.log("⏰ 停止定期检查插件激活状态"),a())},12e4),window.addEventListener("storage",e=>{"globalTimezoneSpoofer_selectedCity"===e.key&&(t||console.log("📢 检测到地区设置变化"),setTimeout(()=>n(t),100))})}()}(),"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.onMessage.addListener((e,t,o)=>{"REGION_SETTINGS_UPDATED"===e.type&&(console.log("📢 收到地区设置更新消息"),setTimeout(()=>{n()},100),o({success:!0}))})}();